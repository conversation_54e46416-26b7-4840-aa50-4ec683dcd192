.history-page { padding: 32rpx; background: #f7f8fa; min-height: 100vh; }
.loading, .empty { text-align: center; color: #aaa; margin-top: 80rpx; }
.history-item { background: #fff; border-radius: 12rpx; margin-bottom: 32rpx; padding: 24rpx; box-shadow: 0 2rpx 8rpx #eee; }
.item-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 12rpx; }
.type { color: #1890ff; font-weight: 600; }
.status { color: #faad14; font-size: 26rpx; }
.content { color: #333; font-size: 30rpx; margin-bottom: 16rpx; }

/* 附件样式 */
.attachments { margin: 16rpx 0; }
.attachment-title { color: #666; font-size: 26rpx; margin-bottom: 12rpx; }
.attachment-list { display: flex; flex-wrap: wrap; gap: 12rpx; }
.attachment-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 8rpx;
  min-width: 200rpx;
  max-width: 300rpx;
}
.attachment-thumb {
  width: 60rpx;
  height: 60rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}
.file-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e6f7ff;
  border-radius: 6rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}
.file-icon .icon { font-size: 32rpx; }
.attachment-info {
  flex: 1;
  min-width: 0;
}
.attachment-name {
  display: block;
  font-size: 24rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.attachment-size {
  display: block;
  font-size: 20rpx;
  color: #999;
  margin-top: 4rpx;
}

.reply { margin: 16rpx 0; font-size: 26rpx; color: #666; background: #f0f9ff; padding: 12rpx; border-radius: 8rpx; }
.reply-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8rpx; }
.reply-label { color: #888; margin-right: 8rpx; font-weight: 600; }
.reply-meta { display: flex; flex-direction: column; align-items: flex-end; gap: 4rpx; }
.reply-handler { color: #666; font-size: 22rpx; }
.reply-time { color: #999; font-size: 20rpx; }
.reply-content { color: #333; line-height: 1.4; }

.meta { display: flex; align-items: center; gap: 16rpx; margin-top: 16rpx; }
.time { color: #aaa; font-size: 24rpx; }